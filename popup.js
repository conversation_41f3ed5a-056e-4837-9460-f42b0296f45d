document.addEventListener("DOMContentLoaded", async () => {
  // --- Selectors ---
  const loading = document.getElementById("loading");
  const results = document.getElementById("results");
  const errorBox = document.getElementById("error");
  const historyBox = document.getElementById("history");
  const historyList = document.getElementById("history-list");

  const verdictText = document.getElementById("verdict-text").querySelector("span");
  const confidenceLabel = document.getElementById("confidence-label");
  const progressCircle = document.querySelector(".progress-ring__circle");

  const explainToggle = document.getElementById("explain-toggle");
  const explanation = document.getElementById("explanation");
  const retryBtn = document.getElementById("retry-btn");
  const darkToggle = document.getElementById("dark-toggle");
  const historyBtn = document.getElementById("history-btn");

  const typeBadge = document.getElementById("type-badge");

  // --- NEW: pull lastCheck from background/content ---
  let pendingType = "image";
  let pendingValue = null;
  try {
    const { lastCheck } = await chrome.storage.local.get("lastCheck");
    if (lastCheck?.type && (lastCheck.data || lastCheck.value)) {
      pendingType = lastCheck.type;
      pendingValue = lastCheck.data || lastCheck.value;
    }
  } catch {}

  // --- Fake API simulation ---
  function fetchResult() {
    loading.classList.remove("hidden");
    results.classList.add("hidden");
    errorBox.classList.add("hidden");

    setTimeout(() => {
      loading.classList.add("hidden");

      if (Math.random() > 0.2) {
        const mock = {
          type: pendingType,
          value: pendingValue || (pendingType === "text" ? "Sample selected text…" : "demo.jpg"),
          verdict: Math.random() > 0.5 ? "Real" : "Fake",
          confidence: Math.floor(Math.random() * 40) + 60,
          explanation: "This is a dummy AI explanation with plausible text for testing UI.",
          source: { url: "https://www.reuters.com", name: "Reuters" }
        };
        showResults(mock);
        saveToHistory(mock);
        renderHistory();
      } else {
        errorBox.classList.remove("hidden");
      }
    }, 1500);
  }

  // --- Show results ---
  function showResults(data) {
    results.classList.remove("hidden");
    verdictText.textContent = data.verdict;

    // Type badge
    typeBadge.textContent = data.type === "image" ? "🖼️ Image" : "✍️ Text";

    // Confidence ring
    let offset = 188 - (188 * data.confidence) / 100;
    progressCircle.style.strokeDashoffset = offset;
    progressCircle.style.stroke = data.verdict === "Fake" ? "#dc2626" : "#22c55e";
    confidenceLabel.textContent = `${data.confidence}%`;

    // Explanation + source
    document.getElementById("explain-text").textContent = data.explanation;
    document.getElementById("source-link").innerHTML =
      `<img src="https://www.google.com/s2/favicons?sz=32&domain=${new URL(data.source.url).hostname}" /> 
       <a href="${data.source.url}" target="_blank">${data.source.name}</a>`;
  }

  // --- History management ---
  function saveToHistory(entry) {
    let history = JSON.parse(localStorage.getItem("verifyHistory")) || [];
    const entryWithTime = { ...entry, time: new Date().toLocaleTimeString() };
    history.unshift(entryWithTime);
    history = history.slice(0, 8); // keep last 8
    localStorage.setItem("verifyHistory", JSON.stringify(history));
  }

  function renderHistory() {
    const history = JSON.parse(localStorage.getItem("verifyHistory")) || [];
    historyList.innerHTML = "";

    if (history.length === 0) {
      historyList.innerHTML = "<li>No checks yet</li>";
      return;
    }

    history.forEach(item => {
      const li = document.createElement("li");

      // Icon + preview + confidence color
      const icon = item.type === "image" ? "🖼️" : "✍️";
      const preview = item.type === "image"
        ? item.value.split("/").pop().slice(0, 12)
        : item.value.slice(0, 20);
      const confColor = item.confidence >= 80 ? "green"
                      : item.confidence >= 50 ? "orange"
                      : "red";

      li.innerHTML = `
        ${icon} <strong>${preview}</strong> 
        <span style="color:${confColor}; font-size:0.85em;">(${item.confidence}%)</span>
      `;
      li.dataset.historyItem = JSON.stringify(item);
      historyList.appendChild(li);
    });
  }

  // --- UI events ---
  explainToggle.addEventListener("click", () => {
    explanation.classList.toggle("hidden");
  });

  retryBtn.addEventListener("click", fetchResult);

  darkToggle.addEventListener("click", () => {
    document.body.classList.toggle("dark");
    if (document.body.classList.contains("dark")) {
      localStorage.setItem("darkMode", "true");
    } else {
      localStorage.removeItem("darkMode");
    }
  });

  historyBtn.addEventListener("click", () => {
    historyBox.classList.toggle("hidden");
    if (!historyBox.classList.contains("hidden")) {
      renderHistory();
    }
  });

  historyList.addEventListener("click", (e) => {
    if (e.target && e.target.closest("li")?.dataset.historyItem) {
      const itemData = JSON.parse(e.target.closest("li").dataset.historyItem);
      showResults(itemData);
      historyBox.classList.add("hidden");
    }
  });

  document.getElementById("clear-history-btn").addEventListener("click", () => {
    localStorage.removeItem("verifyHistory");
    renderHistory();
    alert("✅ History cleared successfully!");
  });

  // --- Initial load ---
  if (localStorage.getItem("darkMode") === "true") {
    document.body.classList.add("dark");
  }

  fetchResult();
});
