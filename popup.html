<!DOCTYPE html><!-- Set `dark` class on load if needed -->
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Verify It</title>
  <link rel="stylesheet" href="popup.css" />
</head>
<body>
  <header class="header">
    <!-- Corrected logo path -->
    <img src="icons/icon128.png" alt="Verify It" class="logo" />
    <h1>Verify with Confidence</h1>
  </header>

  <!-- Loading Skeleton -->
  <div id="loading" class="card loading">
    <div class="skeleton skeleton-text"></div>
    <div class="skeleton skeleton-bar"></div>
  </div>

  <!-- Results Section -->
  <div id="results" class="hidden">
    <div class="card verdict-card">
    <h2 id="verdict-text">
        Verdict: <span>—</span> 
        <span id="type-badge" class="badge">—</span>
    </h2>
    <div class="confidence-container">
        <svg class="progress-ring" width="80" height="80">
        <circle class="progress-ring__bg" stroke-width="8" r="30" cx="40" cy="40" />
        <circle class="progress-ring__circle" stroke-width="8" r="30" cx="40" cy="40" />
        </svg>
        <span id="confidence-label">0%</span>
    </div>
    </div>


    <div class="card collapsible">
      <button class="collapse-btn" id="explain-toggle">Why?</button>
      <div id="explanation" class="content hidden">
        <p id="explain-text">AI explanation goes here…</p>
      </div>
    </div>

    <div class="card source-card">
      <h3>Fact-check Source</h3>
      <div id="source-link">
        <!-- Placeholder icon -->
        <img src="icons/icon128.png" width="16" />
        <a href="#" target="_blank">example.com</a>
      </div>
    </div>
  </div>

  <!-- Error State -->
  <div id="error" class="card error hidden">
    <p>⚠️ Could not verify right now.</p>
    <button id="retry-btn">Try Again</button>
  </div>

  <!-- History Section -->
  <div id="history" class="card hidden">
    <h3>History</h3>
    <!-- List items will be clickable -->
    <ul id="history-list"></ul>
     <button id="clear-history-btn" class="clear-btn">Clear History</button>
 </div>

  <footer class="footer">
    <button id="history-btn">History</button>
    <button id="dark-toggle">🌙 Dark Mode</button>
  </footer>

  <!-- <script src="background.js"></script> -->


  <script src="popup.js"></script>
</body>
</html>
