body {
  font-family: "Segoe UI", <PERSON><PERSON>, sans-serif;
  margin: 0;
  padding: 12px;
  width: 320px;
  background: #f9fafb;
  color: #111827;
  transition: background 0.3s, color 0.3s;
}

.header {
  text-align: center;
  margin-bottom: 12px;
}

.logo {
  width: 48px;
  height: 48px;
}

h1 {
  font-size: 16px;
  margin: 6px 0 0;
  color: #2563eb;
}

.card {
  background: white;
  border-radius: 14px;
  padding: 12px;
  margin-bottom: 10px;
  box-shadow: 0 2px 6px rgba(0,0,0,0.08);
}

.verdict-card h2 {
  font-size: 18px;
  margin: 0;
}

.confidence-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 10px;
  position: relative;
}

.progress-ring__bg {
  fill: transparent;
  stroke: #e5e7eb;
}

.progress-ring__circle {
  fill: transparent;
  stroke: #22c55e;
  stroke-dasharray: 188;
  stroke-dashoffset: 188;
  transition: stroke-dashoffset 0.6s ease, stroke 0.3s;
}

#confidence-label {
  position: absolute;
  font-weight: bold;
  font-size: 16px;
}

.collapsible .collapse-btn {
  background: none;
  border: none;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  color: #2563eb;
}

.hidden { display: none; }

.source-card a {
  margin-left: 6px;
  color: #2563eb;
  text-decoration: none;
}

.source-card img {
  vertical-align: middle;
}

.error {
  background: #fee2e2;
  border-left: 4px solid #dc2626;
  color: #b91c1c;
}

.footer {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
}

.footer button {
  border: 1px solid #d1d5db;
  background: white;
  padding: 6px 10px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.footer button:hover {
  background-color: #f3f4f6;
}

/* --- Dark Mode --- */
body.dark {
  background: #1f2937;
  color: #f9fafb;
}

body.dark .card {
  background: #374151;
  box-shadow: 0 2px 6px rgba(0,0,0,0.3);
}

body.dark h1, body.dark .collapsible .collapse-btn, body.dark .source-card a {
  color: #60a5fa;
}

body.dark .progress-ring__bg {
  stroke: #4b5563;
}

body.dark .error {
  background: #5c2b2b;
  color: #fecaca;
}

body.dark .footer button {
  background: #4b5563;
  border-color: #6b7280;
  color: #f9fafb;
}

body.dark .footer button:hover {
  background-color: #6b7280;
}

/* --- Loading Skeleton --- */
.loading .skeleton {
  background-color: #e5e7eb;
  border-radius: 4px;
}
body.dark .loading .skeleton {
  background-color: #4b5563;
}
.skeleton-text { height: 20px; width: 80%; margin-bottom: 10px; }
.skeleton-bar { height: 40px; width: 100%; }

/* --- History --- */
#history-list {
  list-style: none;
  padding: 0;
  margin: 0;
  max-height: 150px;
  overflow-y: auto;
}

#history-list li {
  padding: 8px;
  border-bottom: 1px solid #e5e7eb;
  cursor: pointer;
  transition: background-color 0.2s;
}
body.dark #history-list li {
  border-bottom-color: #4b5563;
}

#history-list li:hover {
  background-color: #f3f4f6;
}
body.dark #history-list li:hover {
  background-color: #4b5563;
}

#history-list li:last-child {
  border-bottom: none;
}

.clear-btn {
  margin-top: 10px;
  width: 100%;
  border: 1px solid #d1d5db;
  background: white;
  padding: 6px 10px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.clear-btn:hover {
  background-color: #f3f4f6;
}

body.dark .clear-btn {
  background: #4b5563;
  border-color: #6b7280;
  color: #f9fafb;
}

body.dark .clear-btn:hover {
  background-color: #6b7280;
}