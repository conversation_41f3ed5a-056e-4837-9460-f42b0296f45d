chrome.runtime.onInstalled.addListener(() => {
  // Context menu for images
  chrome.contextMenus.create({
    id: "verify-image",
    title: "Verify this image",
    contexts: ["image"]
  });

  // Context menu for selected text
  chrome.contextMenus.create({
    id: "verify-text",
    title: "Verify selected text",
    contexts: ["selection"]
  });
});

chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === "verify-image") {
    chrome.storage.local.set({
      lastCheck: {
        type: "image",
        data: info.srcUrl,
        timestamp: Date.now()
      }
    });
    chrome.action.openPopup(); // opens popup with latest data
  }

  if (info.menuItemId === "verify-text") {
    chrome.storage.local.set({
      lastCheck: {
        type: "text",
        data: info.selectionText,
        timestamp: Date.now()
      }
    });
    chrome.action.openPopup();
  }
});

// Messages from content script "floating bubble" (Verify button)
chrome.runtime.onMessage.addListener((msg, sender, sendResponse) => {
  if (msg?.type === "VERIFY_TEXT_FROM_CONTENT") {
    chrome.storage.local.set({
      lastCheck: { type: "text", value: msg.text, ts: Date.now() }
    }, () => chrome.action.openPopup());

    sendResponse({ ok: true });
    return true;
  }
});
