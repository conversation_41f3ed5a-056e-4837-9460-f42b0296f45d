(function () {
  let bubble = null;

  // Create bubble element
  function createBubble(selectedText, x, y) {
    removeBubble(); // clear old one first

    bubble = document.createElement("div");
    bubble.className = "verify-bubble";

    bubble.innerHTML = `
      <button data-action="verify">✔ Verify this</button>
      <button data-action="copy">📋 Copy text</button>
      <button data-action="explain">💡 Explain this</button>
      <button data-action="images">🖼 Related images</button>
    `;

    document.body.appendChild(bubble);

    // Position bubble near selection
    bubble.style.left = x + "px";
    bubble.style.top = y + "px";

    // Add click actions
    bubble.querySelectorAll("button").forEach((btn) => {
      btn.addEventListener("click", () => {
        const action = btn.dataset.action;

        if (action === "verify") {
          chrome.runtime.sendMessage(
            { type: "VERIFY_TEXT_FROM_CONTENT", text: selectedText },
            (res) => console.log("Verify message sent:", res)
          );
        }

        if (action === "copy") {
          navigator.clipboard.writeText(selectedText);
        }

        if (action === "explain") {
          alert("🔎 Explain: (hook your AI API here)\n\n" + selectedText);
        }

        if (action === "images") {
          alert("🖼 Related images search (hook API here)");
        }

        removeBubble();
      });
    });
  }

  // Remove old bubble
  function removeBubble() {
    if (bubble) {
      bubble.remove();
      bubble = null;
    }
  }

  // Listen for text selection
  document.addEventListener("mouseup", (e) => {
    const selection = window.getSelection().toString().trim();
    if (selection) {
      const rect = window.getSelection().getRangeAt(0).getBoundingClientRect();
      createBubble(selection, rect.right + window.scrollX, rect.top + window.scrollY - 30);
    } else {
      removeBubble();
    }
  });

  // Clean up on scroll/click
  document.addEventListener("scroll", removeBubble);
  document.addEventListener("mousedown", (e) => {
    if (bubble && !bubble.contains(e.target)) {
      removeBubble();
    }
  });
})();
